import { Exercise } from "./exercises.types";

export type AssignmentStatus =
  | "PENDING"
  | "IN_PROGRESS"
  | "COMPLETED"
  | "OVERDUE";

export interface Assignment {
  id: string;
  exerciseId: string;
  coachId: string;
  coacheeId: string;
  status: AssignmentStatus;
  dueDate?: string;
  coachNotes?: string | null;
  aiSummary?: string | null;
  exercise: Exercise;
  createdAt: string;
  updatedAt: string;
}

export interface CreateAssignmentData {
  exerciseId: string;
  coacheeId: string;
  dueDate?: string;
}

export interface UpdateAssignmentStatus {
  status: AssignmentStatus;
}

export interface SubmitAnswersData {
  answers: {
    questionId: string;
    responseText?: string;
    likertResponse?: number;
    taskCompleted?: boolean;
    tableResponse?: Record<string, string | number>[];
    graphResponse?: { x: number; y: number }[];
  }[];
}
