export type QuestionType =
  | "free_text"
  | "likert"
  | "task"
  | "table"
  | "graph_2d";

export interface QuestionBase {
  prompt: string;
  description?: string;
  required?: boolean;
  type: QuestionType;
}

export interface FreeTextQuestion extends QuestionBase {
  type: "free_text";
  /** Optional placeholder text for the input field. */
  placeholder?: string;
  /** Optional maximum length for the text response. */
  maxLength?: number;
}

export type LikertLabelMap = Record<number, string>;

interface LikertQuestionBase extends QuestionBase {
  type: "likert";
  /** The minimum value on the Likert scale. */
  scaleMin: number;
  /** The maximum value on the Likert scale. */
  scaleMax: number;
  /** Captions explaining specific points on the scale. */
  labels: LikertLabelMap;
}

export interface SingleResponseLikertQuestion extends LikertQuestionBase {
  responseMode: "single";
}
export interface MultiResponseLikertQuestion extends LikertQuestionBase {
  responseMode: "multi";
  /** Labels for each response input field (e.g., ["Current", "Goal"]). Required when response_mode is 'multi'. */
  multiResponseLabels: string[];
}

export type LikertQuestion =
  | SingleResponseLikertQuestion
  | MultiResponseLikertQuestion;

export interface TaskQuestion extends QuestionBase {
  type: "task";
  isCompleted: boolean;
}

export type TableColumnType = "free_text" | "likert";

interface TableColumnBase {
  id: string;
  header: string;
  columnType: TableColumnType;
}

export interface FreeTextTableColumn extends TableColumnBase {
  columnType: "free_text";
  placeholder?: string;
}

export interface LikertTableColumn extends TableColumnBase {
  columnType: "likert";
  scaleMin: number;
  scaleMax: number;
  labels: LikertLabelMap;
}

export type TableColumn = FreeTextTableColumn | LikertTableColumn;

export interface TableQuestionBase extends QuestionBase {
  type: "table";
  columns: TableColumn[];
}

export interface DynamicRowTableQuestion extends TableQuestionBase {
  rowMode: "dynamic";
}
export interface FixedRowTableQuestion extends TableQuestionBase {
  rowMode: "fixed";
  fixedRows: number;
}

export type TableQuestion = DynamicRowTableQuestion | FixedRowTableQuestion;

export interface GraphPoint {
  x: number;
  y: number;
}

export interface Graph2DQuestion extends QuestionBase {
  type: "graph_2d";
  xAxisLabel: string;
  yAxisLabel: string;
  xAxisMin?: number;
  xAxisMax?: number;
  yAxisMin?: number;
  yAxisMax?: number;
}

export type Question =
  | FreeTextQuestion
  | LikertQuestion
  | TaskQuestion
  | TableQuestion
  | Graph2DQuestion;

export type QuestionInput = Question & {
  isEditing?: boolean;
};
