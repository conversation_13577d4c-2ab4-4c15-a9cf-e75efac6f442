import { Edit } from "lucide-react";
import { QuestionInput as QuestionInputType } from "../../types/exercise.types";
import { But<PERSON> } from "../ui/Button";
import RichTextDisplay from "../ui/RichTextDisplay";
import { LikertResponse } from "../ui/form/LikertResponse";
import { TableResponse } from "../ui/form/TableResponse";
import { Graph2DResponse } from "../ui/form/Graph2DResponse";
import { useState } from "react";
import Checkbox from "../ui/input/Checkbox";

const QuestionPreview = ({
  question,
  setIsEditing,
}: {
  question: QuestionInputType;
  setIsEditing: () => void;
}) => {
  const [value, setValue] = useState<any>(undefined);

  console.log(value);

  switch (question.type) {
    case "free_text":
      return (
        <div className="border p-4 rounded-md shadow-sm mt-1 relative">
          <Button
            className="absolute top-2 right-2"
            variant="secondary"
            onClick={setIsEditing}
          >
            <Edit size={14} />
          </Button>
          <h3 className="text-lg font-semibold">{question.prompt}</h3>
          {question.description && (
            <RichTextDisplay
              content={question.description}
              className="text-sm text-gray-500 mt-1"
            />
          )}
          <input
            type="text"
            placeholder={question.placeholder || "Type your answer here..."}
            maxLength={question.maxLength}
            className="mt-2 p-2 border rounded w-full"
          />
        </div>
      );
    case "likert":
      return (
        <div className="border p-4 rounded-md shadow-sm mt-1 relative">
          <Button
            className="absolute top-2 right-2"
            variant="secondary"
            onClick={setIsEditing}
          >
            <Edit size={14} />
          </Button>
          <h3 className="text-lg font-semibold">{question.prompt}</h3>
          {question.description && (
            <RichTextDisplay
              content={question.description}
              className="text-sm text-gray-500 mt-1"
            />
          )}
          <LikertResponse
            value={value}
            responseMode={question.responseMode}
            scaleMin={question.scaleMin}
            scaleMax={question.scaleMax}
            labels={question.labels}
            multiResponseLabels={
              "multiResponseLabels" in question
                ? question.multiResponseLabels
                : []
            }
            onChange={setValue}
            isCompleted={false}
            saving={false}
          />
        </div>
      );
    case "task":
      return (
        <div className="border p-4 rounded-md shadow-sm mt-1 relative">
          <Button
            className="absolute top-2 right-2"
            variant="secondary"
            onClick={setIsEditing}
          >
            <Edit size={14} />
          </Button>
          <div className="flex gap-2">
            <Checkbox checked={!!value} onChange={() => setValue(!value)} />
            <div>
              <h3 className="text-lg font-semibold">{question.prompt}</h3>
              {question.description && (
                <RichTextDisplay
                  content={question.description}
                  className="text-sm text-gray-500 mt-1"
                />
              )}
            </div>
          </div>
        </div>
      );
    case "table":
      return (
        <div className="border p-4 rounded-md shadow-sm mt-1 relative">
          <Button
            className="absolute top-2 right-2"
            variant="secondary"
            onClick={setIsEditing}
          >
            <Edit size={14} />
          </Button>
          <h3 className="text-lg font-semibold">{question.prompt}</h3>
          {question.description && (
            <RichTextDisplay
              content={question.description}
              className="text-sm text-gray-500 mt-1"
            />
          )}
          <TableResponse
            columns={question.columns}
            value={value || []}
            onChange={setValue}
            isCompleted={false}
            saving={false}
            rowMode={question.rowMode}
            fixedRows={
              question.rowMode === "fixed" ? question.fixedRows : undefined
            }
          />
        </div>
      );
    case "graph_2d":
      return (
        <div className="border p-4 rounded-md shadow-sm mt-1 relative">
          <Button
            className="absolute top-2 right-2"
            variant="secondary"
            onClick={setIsEditing}
          >
            <Edit size={14} />
          </Button>
          <h3 className="text-lg font-semibold">{question.prompt}</h3>
          {question.description && (
            <RichTextDisplay
              content={question.description}
              className="text-sm text-gray-500 mt-1"
            />
          )}
          <Graph2DResponse
            xAxisLabel={question.xAxisLabel}
            yAxisLabel={question.yAxisLabel}
            xAxisMin={question.xAxisMin}
            xAxisMax={question.xAxisMax}
            yAxisMin={question.yAxisMin}
            yAxisMax={question.yAxisMax}
            value={value || []}
            onChange={setValue}
            isCompleted={false}
            saving={false}
            showBestFit
          />
        </div>
      );
    default:
      return (
        <div>
          <p className="text-red-600">Error: Invalid question type</p>
        </div>
      );
  }
};

export default QuestionPreview;
