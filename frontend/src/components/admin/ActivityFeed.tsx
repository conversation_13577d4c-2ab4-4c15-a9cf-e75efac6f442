import {
  ClipboardCheck,
  UserPlus,
  <PERSON>ting<PERSON>,
  <PERSON>,
  Eye,
  CheckCircle,
} from "lucide-react";
import {
  ActivityLog,
  ActivityEventType,
} from "../../types/api/activityLog.types";

interface ActivityFeedProps {
  activities: ActivityLog[];
  loading?: boolean;
}

const ActivityFeed = ({ activities, loading = false }: ActivityFeedProps) => {
  const getActivityIcon = (type: ActivityEventType) => {
    switch (type) {
      case "EXERCISE_ASSIGNED":
        return ClipboardCheck;
      case "EXERCISE_COMPLETED":
        return CheckCircle;
      case "VISUALIZATION_ASSIGNED":
        return Eye;
      case "VISUALIZATION_COMPLETED":
        return CheckCircle;
      case "COACH_ASSIGNED":
        return UserPlus;
      case "FEEDBACK_PROVIDED":
        return Settings;
      default:
        return Clock;
    }
  };

  const getActivityColor = (type: ActivityEventType) => {
    switch (type) {
      case "EXERCISE_ASSIGNED":
        return "text-blue-600 bg-blue-100";
      case "EXERCISE_COMPLETED":
        return "text-green-600 bg-green-100";
      case "VISUALIZATION_ASSIGNED":
        return "text-purple-600 bg-purple-100";
      case "VISUALIZATION_COMPLETED":
        return "text-green-600 bg-green-100";
      case "COACH_ASSIGNED":
        return "text-orange-600 bg-orange-100";
      case "FEEDBACK_PROVIDED":
        return "text-gray-600 bg-gray-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-start space-x-3 animate-pulse">
            <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (activities.length === 0) {
    return (
      <div className="text-center py-8">
        <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No recent activity
        </h3>
        <p className="text-gray-500">
          Activity will appear here as users interact with the system.
        </p>
      </div>
    );
  }

  return (
    <div className="flow-root">
      <ul className="-mb-8">
        {activities.map((activity, activityIdx) => {
          const Icon = getActivityIcon(activity.eventType);
          const colorClasses = getActivityColor(activity.eventType);

          return (
            <li key={activity.id}>
              <div className="relative pb-8">
                {activityIdx !== activities.length - 1 ? (
                  <span
                    className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                    aria-hidden="true"
                  />
                ) : null}
                <div className="relative flex space-x-3">
                  <div>
                    <span
                      className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${colorClasses}`}
                    >
                      <Icon className="h-4 w-4" aria-hidden="true" />
                    </span>
                  </div>
                  <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {activity.eventMessage}
                      </p>
                      <div className="text-xs text-gray-500 mt-1 space-y-1">
                        {activity.coach && (
                          <p>
                            Coach: {activity.coach.firstName}{" "}
                            {activity.coach.lastName}
                          </p>
                        )}
                        {activity.coachee && (
                          <p>
                            Coachee: {activity.coachee.firstName}{" "}
                            {activity.coachee.lastName}
                          </p>
                        )}
                        {activity.assignment && (
                          <p>Exercise: {activity.assignment.exercise.name}</p>
                        )}
                        {activity.visualizationAssignment && (
                          <p>
                            Visualization:{" "}
                            {
                              activity.visualizationAssignment.visualization
                                .title
                            }
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="whitespace-nowrap text-right text-sm text-gray-500">
                      <time dateTime={activity.timestamp}>
                        {new Date(activity.timestamp).toLocaleDateString()}
                      </time>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default ActivityFeed;
