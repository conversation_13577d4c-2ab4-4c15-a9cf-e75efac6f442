import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAdmin } from "../../context/AdminContext";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { DataTable } from "../ui/DataTable";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { Button } from "../ui/Button";
import { Column } from "../../types/components.types";
import { Exercise } from "../../types/api/exercises.types";
import { BookOpen, Calendar, Eye, Edit, FileText, Trash2 } from "lucide-react";

const AdminExercises = () => {
  const navigate = useNavigate();
  const {
    allExercises,
    loading,
    error,
    fetchAllExercises,
    deleteExerciseById,
  } = useAdmin();

  useEffect(() => {
    fetchAllExercises();
  }, [fetchAllExercises]);

  const handleDeleteExercise = async (exercise: Exercise) => {
    const confirmMessage = `Are you sure you want to delete "${exercise.name}"?\n\nThis will also delete all assignments and submissions associated with this exercise. This action cannot be undone.`;

    if (window.confirm(confirmMessage)) {
      try {
        await deleteExerciseById(exercise.id);
      } catch (error) {
        console.error("Failed to delete exercise:", error);
        // Error is already handled in the context
      }
    }
  };

  const columns: Column<Exercise>[] = [
    {
      header: "Exercise",
      accessorKey: "name",
      cell: (exercise) => (
        <div className="flex items-center">
          <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
            <BookOpen className="h-5 w-5 text-green-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">{exercise.name}</p>
            <p className="text-xs text-gray-500">
              {exercise.description
                ? (() => {
                    // Strip HTML tags for preview
                    const plainText = exercise.description.replace(
                      /<[^>]*>/g,
                      ""
                    );
                    return plainText.length > 50
                      ? `${plainText.substring(0, 50)}...`
                      : plainText;
                  })()
                : "No description"}
            </p>
          </div>
        </div>
      ),
    },
    {
      header: "Type",
      accessorKey: "id",
      cell: (exercise) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
          {exercise.questions?.length || 0} questions
        </span>
      ),
    },
    {
      header: "Created",
      accessorKey: "createdAt",
      cell: (exercise) => (
        <div className="flex items-center">
          <Calendar className="h-4 w-4 text-gray-400 mr-2" />
          <span className="text-sm text-gray-500">
            {new Date(exercise.createdAt).toLocaleDateString()}
          </span>
        </div>
      ),
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: (exercise) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => navigate(`/dashboard/exercises/${exercise.id}`)}
          >
            <Eye className="h-3 w-3 mr-1" />
            View
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => navigate(`/dashboard/exercises/edit/${exercise.id}`)}
          >
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleDeleteExercise(exercise)}
            className="text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Delete
          </Button>
        </div>
      ),
    },
  ];
  if (loading.exercises) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Exercises"
        description="Manage exercises and content"
        actionButton={{
          label: "Create Exercise",
          onClick: () => navigate("/dashboard/exercises/create"),
        }}
      />

      {error && <ErrorMessage message={error} />}

      {/* Exercise Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <div className="flex items-center">
            <BookOpen className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Total Exercises
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {allExercises.length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <FileText className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Total Questions
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {allExercises.reduce(
                  (sum, exercise) => sum + (exercise.questions?.length || 0),
                  0
                )}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Calendar className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">This Month</p>
              <p className="text-lg font-semibold text-gray-900">
                {
                  allExercises.filter((exercise) => {
                    const created = new Date(exercise.createdAt);
                    const now = new Date();
                    return (
                      created.getMonth() === now.getMonth() &&
                      created.getFullYear() === now.getFullYear()
                    );
                  }).length
                }
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Exercises Table */}
      <Card>
        <CardHeader title="All Exercises" />
        <div className="p-6 pt-0">
          <DataTable columns={columns} data={allExercises} className="w-full" />
        </div>
      </Card>
    </div>
  );
};

export default AdminExercises;
