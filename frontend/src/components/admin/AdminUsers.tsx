import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAdmin } from "../../context/AdminContext";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";
import { DataTable } from "../ui/DataTable";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { Column } from "../../types/components.types";
import { User } from "../../types/api/user.types";
import {
  User as UserIcon,
  Mail,
  Shield,
  UserCheck,
  Users,
  Building2,
  Eye,
  Edit,
} from "lucide-react";

const AdminUsers = () => {
  const navigate = useNavigate();
  const { allUsers, loading, error, fetchAllUsers, clearError } = useAdmin();

  useEffect(() => {
    fetchAllUsers();
  }, [fetchAllUsers]);

  const getRoleIcon = (role: User["role"]) => {
    switch (role) {
      case "ADMIN":
        return Shield;
      case "HR_ADMIN":
        return Building2;
      case "COACH":
        return UserCheck;
      case "COACHEE":
        return Users;
      default:
        return UserIcon;
    }
  };

  const getRoleColor = (role: User["role"]) => {
    switch (role) {
      case "ADMIN":
        return "bg-purple-100 text-purple-800";
      case "HR_ADMIN":
        return "bg-blue-100 text-blue-800";
      case "COACH":
        return "bg-green-100 text-green-800";
      case "COACHEE":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const columns: Column<User>[] = [
    {
      header: "User",
      accessorKey: "firstName",
      cell: (user) => {
        const RoleIcon = getRoleIcon(user.role);
        return (
          <div className="flex items-center">
            <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
              <RoleIcon className="h-5 w-5 text-gray-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">
                {user.firstName} {user.lastName}
              </p>
              <p className="text-xs text-gray-500">{user.email}</p>
            </div>
          </div>
        );
      },
    },
    {
      header: "Role",
      accessorKey: "role",
      cell: (user) => (
        <span
          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(
            user.role
          )}`}
        >
          {user.role.replace("_", " ")}
        </span>
      ),
    },
    {
      header: "Contact",
      accessorKey: "email",
      cell: (user) => (
        <div className="flex items-center">
          <Mail className="h-4 w-4 text-gray-400 mr-2" />
          <span className="text-sm text-gray-900">{user.email}</span>
        </div>
      ),
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: (user) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
          Active
        </span>
      ),
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: (user) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => navigate(`/dashboard/users/${user.id}`)}
          >
            <Eye className="h-3 w-3 mr-1" />
            View
          </Button>
          <Button size="sm" variant="outline">
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
        </div>
      ),
    },
  ];

  if (loading.users) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Users"
        description="Manage system users and their roles"
        actionButton={{
          label: "Add User",
          onClick: () => navigate("/dashboard/users/create"),
        }}
      />

      {error && <ErrorMessage message={error} onDismiss={clearError} />}

      {/* User Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center">
            <Shield className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Admins</p>
              <p className="text-lg font-semibold text-gray-900">
                {allUsers.filter((u) => u.role === "ADMIN").length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Building2 className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">HR Admins</p>
              <p className="text-lg font-semibold text-gray-900">
                {allUsers.filter((u) => u.role === "HR_ADMIN").length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <UserCheck className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Coaches</p>
              <p className="text-lg font-semibold text-gray-900">
                {allUsers.filter((u) => u.role === "COACH").length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-orange-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Coachees</p>
              <p className="text-lg font-semibold text-gray-900">
                {allUsers.filter((u) => u.role === "COACHEE").length}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Users Table */}
      <Card>
        <CardHeader title="All Users" />
        <div className="p-6 pt-0">
          <DataTable columns={columns} data={allUsers} className="w-full" />
        </div>
      </Card>
    </div>
  );
};

export default AdminUsers;
