import { Route, Routes } from "react-router";
import { LoadingSpinner } from "../components/ui/LoadingSpinner";
import React from "react";
import CoachHome from "../components/coach/CoachHome";
import CoachSidebar from "../components/coach/CoachSidebar";
import Container from "../components/ui/Container";
import CoachCoachees from "../components/coach/CoachCoachees";
import { CoachProvider } from "../context/CoachContext";
import CoachExercises from "../components/coach/CoachExercises";
import CoachCreateAssignment from "../components/coach/CoachCreateAssignment";
import CoachCreateVisualizationAssignment from "../components/coach/CoachCreateVisualizationAssignment";
import CoachCreateVisualization from "../components/coach/CoachCreateVisualization";
import CoachVisualizations from "../components/coach/CoachVisualizations";
import CoachAssignCoachee from "../components/coach/CoachAssignCoachee";
import CoachCreateEvaluation from "../components/coach/CoachCreateEvaluation";
import CoachErrorBoundary from "../components/coach/CoachErrorBoundary";

export default function CoachDashboard() {
  return (
    <CoachErrorBoundary>
      <CoachProvider>
        <Container>
          <div className="flex flex-row h-full">
            <CoachSidebar />
            <div className="w-full mx-auto overflow-y-auto px-4 sm:px-6 lg:px-8 py-8">
              <Routes>
                <Route
                  path="/"
                  element={
                    <React.Suspense fallback={<LoadingSpinner />}>
                      <CoachHome />
                    </React.Suspense>
                  }
                />
                <Route
                  path="/coachees"
                  element={
                    <React.Suspense fallback={<LoadingSpinner />}>
                      <CoachCoachees />
                    </React.Suspense>
                  }
                />
                <Route
                  path="/assign-coachee"
                  element={
                    <React.Suspense fallback={<LoadingSpinner />}>
                      <CoachAssignCoachee />
                    </React.Suspense>
                  }
                />
                <Route
                  path="/exercises"
                  element={
                    <React.Suspense fallback={<LoadingSpinner />}>
                      <CoachExercises />
                    </React.Suspense>
                  }
                />
                <Route
                  path="/create-assignment"
                  element={
                    <React.Suspense fallback={<LoadingSpinner />}>
                      <CoachCreateAssignment />
                    </React.Suspense>
                  }
                />
                <Route
                  path="/create-visualization-assignment"
                  element={
                    <React.Suspense fallback={<LoadingSpinner />}>
                      <CoachCreateVisualizationAssignment />
                    </React.Suspense>
                  }
                />
                <Route
                  path="/visualizations"
                  element={
                    <React.Suspense fallback={<LoadingSpinner />}>
                      <CoachVisualizations />
                    </React.Suspense>
                  }
                />
                <Route
                  path="/create-visualization"
                  element={
                    <React.Suspense fallback={<LoadingSpinner />}>
                      <CoachCreateVisualization />
                    </React.Suspense>
                  }
                />
                <Route
                  path="/create-evaluation"
                  element={
                    <React.Suspense fallback={<LoadingSpinner />}>
                      <CoachCreateEvaluation />
                    </React.Suspense>
                  }
                />
              </Routes>
            </div>
          </div>
        </Container>
      </CoachProvider>
    </CoachErrorBoundary>
  );
}
