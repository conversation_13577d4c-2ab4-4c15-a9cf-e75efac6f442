import {
  Assignment,
  CreateAssignmentData,
  SubmitAnswersData,
} from "../types/api/assignments.types";
import axiosInstance from "./axiosConfig";

export const getAllAssignments = async (): Promise<Assignment[]> => {
  const response = await axiosInstance.get<Assignment[]>("/assignments");
  return response.data;
};

export const getAssignmentById = async (id: string): Promise<Assignment> => {
  const response = await axiosInstance.get<Assignment>(`/assignments/${id}`);
  return response.data;
};

export const createAssignment = async (
  data: CreateAssignmentData
): Promise<Assignment> => {
  const response = await axiosInstance.post<Assignment>("/assignments", data);
  return response.data;
};

export const submitAnswers = async (
  id: string,
  data: SubmitAnswersData
): Promise<Assignment> => {
  const response = await axiosInstance.post<Assignment>(
    `/assignments/${id}/submit`,
    data
  );
  return response.data;
};

export const deleteAssignment = async (id: string): Promise<void> => {
  await axiosInstance.delete(`/assignments/${id}`);
};
