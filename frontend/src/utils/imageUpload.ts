// Example utility for uploading images to cloud storage
// This is a template - you'll need to implement the actual upload logic

export interface ImageUploadOptions {
  maxSizeBytes?: number;
  allowedTypes?: string[];
  quality?: number;
}

const DEFAULT_OPTIONS: ImageUploadOptions = {
  maxSizeBytes: 5 * 1024 * 1024, // 5MB
  allowedTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
  quality: 0.8,
};

/**
 * Validates an image file before upload
 */
export const validateImageFile = (
  file: File,
  options: ImageUploadOptions = {}
): boolean => {
  const opts = { ...DEFAULT_OPTIONS, ...options };

  // Check file size
  if (opts.maxSizeBytes && file.size > opts.maxSizeBytes) {
    throw new Error(
      `File size exceeds ${opts.maxSizeBytes / (1024 * 1024)}MB limit`
    );
  }

  // Check file type
  if (opts.allowedTypes && !opts.allowedTypes.includes(file.type)) {
    throw new Error(`File type ${file.type} is not allowed`);
  }

  return true;
};

/**
 * Compresses an image file if needed
 */
export const compressImage = (
  file: File,
  quality: number = 0.8
): Promise<File> => {
  return new Promise((resolve) => {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions (max 1200px width)
      const maxWidth = 1200;
      const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
      canvas.width = img.width * ratio;
      canvas.height = img.height * ratio;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            resolve(file);
          }
        },
        file.type,
        quality
      );
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * Upload image to S3 via backend API
 */
export const uploadToS3 = async (file: File): Promise<string> => {
  // Validate file
  validateImageFile(file);

  // Create form data
  const formData = new FormData();
  formData.append("image", file);

  try {
    // Get auth token
    const token = localStorage.getItem("token");
    if (!token) {
      throw new Error("Authentication required");
    }

    const response = await fetch("/api/images/upload", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || "Upload failed");
    }

    const data = await response.json();
    return data.imageUrl; // Return the uploaded image URL
  } catch (error) {
    console.error("Image upload error:", error);
    throw error;
  }
};

/**
 * Simple base64 conversion for local storage
 */
export const convertToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};
