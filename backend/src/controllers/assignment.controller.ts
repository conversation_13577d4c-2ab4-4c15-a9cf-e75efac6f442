import { Request, Response } from "express";
import { AssignmentService } from "../services/assignment.service";

export const assignmentController = {
  // Create a new assignment
  create: async (req: Request, res: Response) => {
    try {
      const { exerciseId, coacheeId, dueDate } = req.body;
      if (!exerciseId || !coacheeId) {
        res
          .status(400)
          .json({ error: "exerciseId and coacheeId are required" });
        return;
      }

      if (!req.user) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }
      // Validate if the user is a coach
      if (req.user.role !== "COACH") {
        res.status(403).json({ error: "Forbidden" });
        return;
      }

      const coachId = req.user.userId;

      const assignment = await AssignmentService.createAssignment({
        exerciseId,
        coacheeId,
        coachId,
        dueDate: dueDate ? new Date(dueDate) : undefined,
      });

      res.status(201).json(assignment);
    } catch (error: any) {
      console.error("Error creating assignment:", error);
      res
        .status(400)
        .json({ error: error.message || "Failed to create assignment" });
    }
  },

  // Get all assignments for a coach or coachee
  getAll: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const role = req.user?.role;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      // Get assignments based on user role
      let assignments;
      if (role === "COACH") {
        assignments = await AssignmentService.getCoachAssignments(userId);
      } else if (role === "COACHEE") {
        assignments = await AssignmentService.getCoacheeAssignments(userId);
      } else {
        // For admin users, get all assignments
        assignments = await AssignmentService.getAssignments();
      }

      res.json(assignments);
    } catch (error: any) {
      console.error("Error fetching assignments:", error);
      res
        .status(400)
        .json({ error: error.message || "Failed to fetch assignments" });
    }
  },

  // Get a single assignment by ID
  getById: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      const assignment = await AssignmentService.getAssignmentById(id);

      if (!assignment) {
        res.status(404).json({ error: "Assignment not found" });
        return;
      }

      // Check if user has access to this assignment (either coach or coachee)
      if (assignment.coachId !== userId && assignment.coacheeId !== userId) {
        res
          .status(403)
          .json({ error: "Not authorized to access this assignment" });
        return;
      }

      res.json(assignment);
    } catch (error: any) {
      console.error("Error fetching assignment:", error);
      res
        .status(400)
        .json({ error: error.message || "Failed to fetch assignment" });
    }
  },

  // Update assignment status
  updateStatus: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { status } = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      const assignment = await AssignmentService.updateAssignment(id, {
        status,
      });

      res.json(assignment);
    } catch (error: any) {
      console.error("Error updating assignment status:", error);
      res
        .status(400)
        .json({ error: error.message || "Failed to update assignment status" });
    }
  },

  // Submit assignment answers
  submit: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { answers } = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      if (!answers || !Array.isArray(answers)) {
        res.status(400).json({ error: "Answers array is required" });
        return;
      }

      // Transform answers array to object format for storage
      const answersObject: Record<string, any> = {};
      answers.forEach((answer: any) => {
        if (answer.questionId) {
          if (answer.responseText !== undefined) {
            answersObject[answer.questionId] = answer.responseText;
          } else if (answer.likertResponse !== undefined) {
            answersObject[answer.questionId] = answer.likertResponse;
          } else if (answer.taskCompleted !== undefined) {
            answersObject[answer.questionId] = answer.taskCompleted;
          } else if (answer.tableResponse !== undefined) {
            answersObject[answer.questionId] = answer.tableResponse;
          } else if (answer.graphResponse !== undefined) {
            answersObject[answer.questionId] = answer.graphResponse;
          }
        }
      });

      const updatedAssignment = await AssignmentService.submitAssignment(
        id,
        userId,
        answersObject
      );

      res.json(updatedAssignment);
    } catch (error: any) {
      console.error("Assignment submission error:", error);
      res
        .status(400)
        .json({ error: error.message || "Failed to submit assignment" });
    }
  },
};
