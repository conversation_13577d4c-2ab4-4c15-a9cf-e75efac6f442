import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import prisma from "../prisma";

export const getCoacheesForCoach: RequestHandler = async (req, res) => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(403).json({ error: "Authentication required" });
      return;
    }
    // Validate if the user is a coach
    if (req.user?.role !== "COACH") {
      res.status(403).json({ error: "Forbidden" });
      return;
    }

    // Fetch coachees for the coach
    const coachees = await prisma.user.findMany({
      where: {
        coacheeRelationships: {
          some: {
            coachId: userId,
          },
        },
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,

        organizationMemberships: {
          select: {
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });
    res.status(200).json(coachees);
  } catch (error) {
    res.status(400).json({ error: "Failed to fetch coachees" });
  }
};

export const addCoachee: RequestHandler = async (req, res) => {
  try {
    const { email } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(403).json({ error: "Authentication required" });
      return;
    }

    // Validate if the user is a coach
    if (req.user?.role !== "COACH") {
      res.status(403).json({ error: "Forbidden" });
      return;
    }

    // Validate if the coacheeId is provided
    if (!email) {
      res.status(400).json({ error: "Coachee Email is required" });
      return;
    }
    // Validate if the coachee exists
    const coachee = await prisma.user.findUnique({
      where: { email },
      select: { id: true, email: true, firstName: true, lastName: true },
    });
    if (!coachee) {
      res.status(404).json({ error: "Coachee not found" });
      return;
    }
    // Validate if the coachee is already assigned to the coach
    const existingRelationship = await prisma.coachCoachee.findFirst({
      where: {
        coachId: userId,
        coacheeId: coachee.id,
      },
    });
    if (existingRelationship) {
      res.status(409).json({ error: "Coachee already assigned to this coach" });
      return;
    }
    // Create the relationship between the coach and coachee
    const newCoachee = await prisma.coachCoachee.create({
      data: {
        coachId: userId,
        coacheeId: coachee.id,
      },
    });
    res.status(201).json(coachee);
  } catch (error) {
    res.status(400).json({ error: `Failed to add coachee: ${error}` });
  }
};

export const addCoachNotes: RequestHandler = async (req, res) => {
  try {
    const { coacheeId, notes } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(403).json({ error: "Authentication required" });
      return;
    }
    // Validate if the user is a coach
    if (req.user?.role !== "COACH") {
      res.status(403).json({ error: "Forbidden" });
      return;
    }

    // Validate if the coacheeId is provided
    if (!coacheeId) {
      res.status(400).json({ error: "Coachee ID is required" });
      return;
    }
    // Validate if the notes are provided
    if (!notes) {
      res.status(400).json({ error: "Notes are required" });
      return;
    }
    // Create the relationship between the coach and coachee
    const newCoachee = await prisma.coachCoachee.update({
      where: {
        coachId_coacheeId: {
          coachId: userId,
          coacheeId,
        },
      },
      data: {
        coachNotes: notes,
      },
    });
    res.status(201).json(newCoachee);
  } catch (error) {
    res.status(400).json({ error: `Failed to add coachee notes: ${error}` });
  }
};

export const getCoacheeDetails: RequestHandler = async (req, res) => {
  try {
    const { coacheeId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(403).json({ error: "Authentication required" });
      return;
    }
    // Validate if the user is a coach
    if (req.user?.role !== "COACH") {
      res.status(403).json({ error: "Forbidden" });
      return;
    }

    // Fetch coachee details for the coach
    const coachee = await prisma.user.findUnique({
      where: {
        id: coacheeId,
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        createdAt: true,
        organizationMemberships: {
          select: {
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        coacheeRelationships: {
          where: {
            coachId: userId,
          },
          select: {
            coachNotes: true,
          },
        },
        assignedAssignments: {
          where: {
            coachId: userId,
          },
          select: {
            id: true,
            dueDate: true,
            status: true,
            coachFeedback: true,
            createdAt: true,
            aiSummary: true,
            feedbackAt: true,
            exercise: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
          },
        },
        assignedVisAssignments: {
          where: {
            coachId: userId,
          },
          select: {
            id: true,
            dueDate: true,
            status: true,
            createdAt: true,
            visualization: {
              select: {
                id: true,
                title: true,
                description: true,
              },
            },
          },
        },
      },
    });

    if (!coachee) {
      res.status(404).json({ error: "Coachee not found" });
      return;
    }

    res.status(200).json(coachee);
  } catch (error) {
    res.status(400).json({ error: "Failed to fetch coachee details" });
  }
};
