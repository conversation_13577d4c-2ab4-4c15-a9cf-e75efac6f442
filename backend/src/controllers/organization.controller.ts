import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import prisma from "../prisma";

// Get all organizations
export const getAllOrganizations: RequestHandler = async (req, res) => {
  try {
    // admin only
    if (req.user?.role !== "ADMIN") {
      res.status(403).json({ message: "Forbidden" });
      return;
    }

    // TODO: Add pagination and filtering (e.g., only orgs user belongs to?)
    const organizations = await prisma.organization.findMany({
      include: {
        coacheeMemberships: true,
        coachMemberships: true,
      },
    });
    res.status(200).json(organizations);
  } catch (error) {
    console.error("Error fetching organizations:", error);
    res.status(500).json({ message: "Failed to fetch organizations" });
  }
};

// Get organization by ID
export const getOrganizationById: RequestHandler = async (req, res) => {
  const { id } = req.params;
  try {
    const organization = await prisma.organization.findUnique({
      where: { id },
      // include: { coachees: true, coaches: true } // Include members
    });
    if (!organization) {
      res.status(404).json({ message: "Organization not found" });
      return;
    }
    // TODO: Add authorization check - does user belong to this org?
    res.status(200).json(organization);
  } catch (error) {
    console.error("Error fetching organization:", error);
    res.status(500).json({ message: "Failed to fetch organization" });
  }
};

// Create new organization
export const createOrganization: RequestHandler = async (req, res) => {
  // TODO: Add validation
  const { name } = req.body;
  const userId = req.user?.userId;

  if (!userId) {
    res.status(403).json({ message: "User ID not found on request" });
    return;
  }

  if (!name) {
    res.status(400).json({ message: "Name is a required field." });
    return;
  }

  try {
    // Create the organization and automatically add the creator as an admin or coach?
    // This requires more complex logic involving roles and potentially OrganizationCoach/Coachee tables.
    // Simple creation for now:
    const newOrganization = await prisma.organization.create({
      data: {
        name,
        // createdById: userId // If you add a createdBy field
      },
    });
    // TODO: Optionally link the creator (userId) to the new org based on role
    res.status(201).json(newOrganization);
    return;
  } catch (error) {
    console.error("Error creating organization:", error);
    res.status(500).json({ message: "Failed to create organization" });
    return;
  }
};

// Update organization
export const updateOrganization: RequestHandler = async (req, res) => {
  const { id } = req.params;
  const { name } = req.body;
  // TODO: Add validation & authorization (is user admin of this org?)
  const userId = req.user?.userId; // Needed for auth check

  if (!userId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }

  if (name === undefined) {
    res
      .status(400)
      .json({ message: "Name field must be provided for update." });
    return;
  }

  try {
    // Optional: Check existence first
    const existingOrg = await prisma.organization.findUnique({ where: { id } });
    if (!existingOrg) {
      res.status(404).json({ message: "Organization not found" });
      return;
    }
    // TODO: Authorization check (is userId an admin/coach of this org?)

    const updatedOrganization = await prisma.organization.update({
      where: { id },
      data: {
        name,
      },
    });
    res.status(200).json(updatedOrganization);
    return;
  } catch (error) {
    if (
      error instanceof Error &&
      error.message.includes("Record to update not found")
    ) {
      // Handled by explicit check above
      res.status(404).json({ message: "Organization not found" });
      return;
    }
    console.error("Error updating organization:", error);
    res.status(500).json({ message: "Failed to update organization" });
    return;
  }
};

// Delete organization
// NOTE: Deleting organizations can be complex due to relations (users, assignments, etc.)
// Consider soft delete (adding an `isActive` flag) or careful cascading deletes.
export const deleteOrganization: RequestHandler = async (req, res) => {
  const { id } = req.params;
  // TODO: Add validation & authorization (is user admin of this org?)
  const userId = req.user?.userId;

  if (!userId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }

  try {
    // WARNING: This performs a hard delete. Ensure cascading deletes are set up correctly
    // in Prisma schema or handle related records manually if needed.

    // Optional: Check existence first
    const existingOrg = await prisma.organization.findUnique({ where: { id } });
    if (!existingOrg) {
      res.status(404).json({ message: "Organization not found" });
      return;
    }
    // TODO: Authorization check

    await prisma.organization.delete({
      where: { id },
    });
    res.status(204).send(); // No content
    return;
  } catch (error) {
    if (
      error instanceof Error &&
      error.message.includes("Record to delete does not exist")
    ) {
      // Handled by explicit check above
      res.status(404).json({ message: "Organization not found" });
      return;
    }
    console.error("Error deleting organization:", error);
    // TODO: Handle FK constraints if users/assignments still link here
    if (
      error instanceof Error &&
      error.message.includes("foreign key constraint")
    ) {
      res.status(409).json({
        message:
          "Cannot delete organization with members or assignments linked.",
      });
      return;
    }
    res.status(500).json({ message: "Failed to delete organization" });
    return;
  }
};

// --- Routes for managing organization members (Coaches/Coachees) ---
// These would likely go in separate controllers/routes for clarity
// or be nested under /organizations/:id/

// Example: Add a Coach to an Organization (Placeholder)
export const addCoachToOrganization: RequestHandler = async (req, res) => {
  const { organizationId } = req.params; // Or get from main route path
  const { userId } = req.body; // ID of the user to add as a coach

  // TODO: Validation (Is organizationId valid? Is userId valid? Is user a COACH?)
  // TODO: Authorization (Is the requesting user an ADMIN of this organization?)
  const requestingUserId = req.user?.userId;
  if (!requestingUserId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }
  if (req.user?.role !== "ADMIN" && req.user?.role !== "HR_ADMIN") {
    res.status(403).json({ message: "Forbidden" });
    return;
  }

  if (req.user?.role === "HR_ADMIN") {
    // check if the user is an HR admin of the organization
    const orgs = await prisma.organization.findMany({
      where: {
        hrAdminId: requestingUserId,
      },
    });
    const orgIds = orgs.map((org) => org.id);
    if (!orgIds.includes(organizationId)) {
      res
        .status(403)
        .json({ message: "HR admin is not the admin of this organization" });
      return;
    }
  }

  try {
    const link = await prisma.organizationCoach.create({
      data: {
        organizationId: organizationId,
        coachId: userId, // Correct field name
      },
    });
    res.status(201).json(link);
    return;
  } catch (error) {
    console.error("Error adding coach to organization:", error);
    // Handle potential errors like user already being in org, FK constraints etc.
    if (
      error instanceof Error &&
      error.message.includes("Unique constraint failed")
    ) {
      res
        .status(409)
        .json({ message: "User is already a coach in this organization" });
      return;
    }
    res.status(500).json({ message: "Failed to add coach to organization" });
    return;
  }
};

// Example: Add a Coachee to an Organization (Placeholder)
export const addCoacheeToOrganization: RequestHandler = async (req, res) => {
  const { organizationId } = req.params;
  const { userId } = req.body; // ID of the user to add as a coachee

  // TODO: Validation & Authorization
  const requestingUserId = req.user?.userId;
  if (!requestingUserId) {
    res.status(403).json({ message: "Authentication required" });
    return;
  }
  if (req.user?.role !== "ADMIN" && req.user?.role !== "HR_ADMIN") {
    res.status(403).json({ message: "Forbidden" });
    return;
  }

  if (req.user?.role === "HR_ADMIN") {
    // check if the user is an HR admin of the organization
    const orgs = await prisma.organization.findMany({
      where: {
        hrAdminId: requestingUserId,
      },
    });
    const orgIds = orgs.map((org) => org.id);
    if (!orgIds.includes(organizationId)) {
      res
        .status(403)
        .json({ message: "HR admin is not the admin of this organization" });
      return;
    }
  }

  try {
    const link = await prisma.organizationCoachee.create({
      data: {
        organizationId: organizationId,
        coacheeId: userId, // Correct field name
      },
    });
    res.status(201).json(link);
    return;
  } catch (error) {
    console.error("Error adding coachee to organization:", error);
    if (
      error instanceof Error &&
      error.message.includes("Unique constraint failed")
    ) {
      res
        .status(409)
        .json({ message: "User is already a coachee in this organization" });
      return;
    }
    res.status(500).json({ message: "Failed to add coachee to organization" });
    return;
  }
};

// ... similarly add removeCoach, removeCoachee, getCoachesInOrg, getCoacheesInOrg etc
