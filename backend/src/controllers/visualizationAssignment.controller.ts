import { Request, Response } from "express";
import { VisualizationAssignmentService } from "../services/visualizationAssignment.service";

export const visualizationAssignmentController = {
  // Create a new visualization assignment
  create: async (req: Request, res: Response) => {
    try {
      const { visualizationId, coacheeId, dueDate } = req.body;

      if (!visualizationId || !coacheeId) {
        res
          .status(400)
          .json({ error: "visualizationId and coacheeId are required" });
        return;
      }
      if (!req.user) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }
      // Validate if the user is a coach
      if (req.user.role !== "COACH") {
        res.status(403).json({ error: "Forbidden" });
        return;
      }

      const coachId = req.user?.userId;

      const assignment =
        await VisualizationAssignmentService.createVisualizationAssignment({
          visualizationId,
          coacheeId,
          coachId,
          dueDate: dueDate ? new Date(dueDate) : undefined,
        });

      res.status(201).json(assignment);
    } catch (error: any) {
      console.error("Error creating visualization assignment:", error);
      res.status(400).json({
        error: error.message || "Failed to create visualization assignment",
      });
    }
  },

  // Get all visualization assignments for a coach or coachee
  getAll: async (req: Request, res: Response) => {
    try {
      const userId = req.user?.userId;
      const role = req.user?.role;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      // Get assignments based on user role
      let assignments;
      if (role === "COACH") {
        assignments =
          await VisualizationAssignmentService.getCoachVisualizationAssignments(
            userId
          );
      } else if (role === "COACHEE") {
        assignments =
          await VisualizationAssignmentService.getCoacheeVisualizationAssignments(
            userId
          );
      } else {
        // For admin users, get all assignments
        assignments =
          await VisualizationAssignmentService.getVisualizationAssignments();
      }

      res.json(assignments);
    } catch (error: any) {
      console.error("Error fetching visualization assignments:", error);
      res.status(400).json({
        error: error.message || "Failed to fetch visualization assignments",
      });
    }
  },

  // Update visualization assignment status
  updateStatus: async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { status } = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(403).json({ error: "Authentication required" });
        return;
      }

      const assignment =
        await VisualizationAssignmentService.updateVisualizationAssignmentStatus(
          id,
          status,
          userId
        );

      res.json(assignment);
    } catch (error: any) {
      console.error("Error updating visualization assignment status:", error);
      res
        .status(400)
        .json({
          error:
            error.message || "Failed to update visualization assignment status",
        });
    }
  },
};
