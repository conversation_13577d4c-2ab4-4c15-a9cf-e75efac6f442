import { Router } from "express";
import {
  generateAudio,
  deleteAudio,
  generateCustomAudio,
  getAvailableVoices,
} from "../controllers/audio.controller";
import { authenticateToken } from "../middleware/auth.middleware";

const router = Router();

// Public routes (no authentication required)
router.get("/voices", getAvailableVoices);

// Apply authentication middleware to protected routes
router.use(authenticateToken);

// Generate audio for a visualization
router.post("/visualizations/:id/audio", generateAudio);

// Generate audio from custom text
router.post("/generate", generateCustomAudio);

// Delete audio for a visualization
router.delete("/visualizations/:id/audio", deleteAudio);

export default router;
