import express from "express";
import { assignment<PERSON>ontroller } from "../controllers/assignment.controller";
import { authenticateToken } from "../middleware/auth.middleware";

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Routes for exercises
router.post("/", assignmentController.create);
router.get("/", assignmentController.getAll);
router.get("/:id", assignmentController.getById);
router.patch("/:id/status", assignmentController.updateStatus);
router.post("/:id/submit", assignmentController.submit);

export default router;
