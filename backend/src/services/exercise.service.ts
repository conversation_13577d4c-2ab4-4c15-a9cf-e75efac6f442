import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export interface CreateExerciseData {
  name: string;
  description?: string;
  questions: any[];
  createdBy: string;
}

export interface UpdateExerciseData {
  name?: string;
  description?: string;
  questions?: any[];
  updatedBy: string;
}

export interface ExerciseFilters {
  createdBy?: string;
  search?: string;
}

export class ExerciseService {
  /**
   * Create a new exercise
   */
  static async createExercise(data: CreateExerciseData) {
    try {
      const exercise = await prisma.exercise.create({
        data: {
          name: data.name,
          description: data.description,
          questions: data.questions,
          createdBy: data.createdBy,
          updatedBy: data.createdBy,
        },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          updater: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return exercise;
    } catch (error) {
      console.error("Error creating exercise:", error);
      throw new Error("Failed to create exercise");
    }
  }

  /**
   * Get all exercises with optional filters
   */
  static async getExercises(filters: ExerciseFilters = {}) {
    try {
      const where: any = {};

      if (filters.createdBy) {
        where.createdBy = filters.createdBy;
      }

      if (filters.search) {
        where.OR = [
          {
            name: {
              contains: filters.search,
              mode: "insensitive",
            },
          },
          {
            description: {
              contains: filters.search,
              mode: "insensitive",
            },
          },
        ];
      }

      const exercises = await prisma.exercise.findMany({
        where,
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          updater: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          assignments: {
            select: {
              id: true,
              status: true,
              createdAt: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return exercises;
    } catch (error) {
      console.error("Error fetching exercises:", error);
      throw new Error("Failed to fetch exercises");
    }
  }

  /**
   * Get exercise by ID
   */
  static async getExerciseById(id: string) {
    try {
      const exercise = await prisma.exercise.findUnique({
        where: { id },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          updater: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          assignments: {
            include: {
              coachee: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
              coach: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
              submission: true,
            },
            orderBy: {
              createdAt: "desc",
            },
          },
        },
      });

      return exercise;
    } catch (error) {
      console.error("Error fetching exercise:", error);
      throw new Error("Failed to fetch exercise");
    }
  }

  /**
   * Update exercise
   */
  static async updateExercise(id: string, data: UpdateExerciseData) {
    try {
      const exercise = await prisma.exercise.update({
        where: { id },
        data: {
          name: data.name,
          description: data.description,
          questions: data.questions,
          updatedBy: data.updatedBy,
          updatedAt: new Date(),
        },
        include: {
          creator: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          updater: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return exercise;
    } catch (error) {
      console.error("Error updating exercise:", error);
      throw new Error("Failed to update exercise");
    }
  }

  /**
   * Delete exercise
   */
  static async deleteExercise(id: string) {
    try {
      // Check if exercise has any assignments
      const assignmentCount = await prisma.assignment.count({
        where: { exerciseId: id },
      });

      if (assignmentCount > 0) {
        throw new Error("Cannot delete exercise with existing assignments");
      }

      await prisma.exercise.delete({
        where: { id },
      });
    } catch (error) {
      console.error("Error deleting exercise:", error);
      throw error;
    }
  }

  /**
   * Get exercises created by a specific user
   */
  static async getExercisesByCreator(createdBy: string) {
    return this.getExercises({ createdBy });
  }

  /**
   * Search exercises
   */
  static async searchExercises(searchTerm: string) {
    return this.getExercises({ search: searchTerm });
  }

  /**
   * Get exercise statistics
   */
  static async getExerciseStats(exerciseId?: string) {
    try {
      const where = exerciseId ? { exerciseId } : {};

      const [
        totalAssignments,
        completedAssignments,
        pendingAssignments,
        inProgressAssignments,
      ] = await Promise.all([
        prisma.assignment.count({ where }),
        prisma.assignment.count({ where: { ...where, status: "COMPLETED" } }),
        prisma.assignment.count({ where: { ...where, status: "PENDING" } }),
        prisma.assignment.count({ where: { ...where, status: "IN_PROGRESS" } }),
      ]);

      return {
        totalAssignments,
        completedAssignments,
        pendingAssignments,
        inProgressAssignments,
        completionRate: totalAssignments > 0 ? Math.round((completedAssignments / totalAssignments) * 100) : 0,
      };
    } catch (error) {
      console.error("Error fetching exercise stats:", error);
      throw new Error("Failed to fetch exercise statistics");
    }
  }
}
